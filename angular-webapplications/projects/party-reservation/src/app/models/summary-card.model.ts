export interface PackageItem {
  name: string;
  price: number;
  quantity: number;
  currency?: string;
  showQuantityBadge?: boolean;
  quantityBadgeText?: string;
}

export interface LocationInfo {
  location: string;
  date: string;
}

export interface SummaryCardResponse {
  title: string;
  basePrice: number;
  currency?: string;
  locationInfo: LocationInfo;
  packageItems: PackageItem[];
}

export const dummySummaryCardResponse: SummaryCardResponse = {
  title: 'Ultimate Adventure Party',
  basePrice: 429.90,
  currency: '$',
  locationInfo: {
    location: 'Semnox Luna',
    date: '17-07-2024'
  },
  packageItems: [
    {
      name: 'Ultimate Adventure',
      price: 0.00,
      quantity: 10,
      currency: '$',
      showQuantityBadge: true,
      quantityBadgeText: 'Price Incl. Qty : 1'
    },
    {
      name: '$20 game credits per guest',
      price: 0.00,
      quantity: 10,
      currency: '$'
    },
    {
      name: '$20 game credits per guest',
      price: 0.00,
      quantity: 10,
      currency: '$'
    },
    {
      name: 'Ultimate Adventure',
      price: 0.00,
      quantity: 10,
      currency: '$',
      showQuantityBadge: true,
      quantityBadgeText: 'Price Incl. Qty : 1'
    },
    {
      name: '$20 game credits per guest',
      price: 0.00,
      quantity: 10,
      currency: '$'
    },
    {
        name: 'Ultimate Adventure',
        price: 0.00,
        quantity: 10,
        currency: '$',
        showQuantityBadge: true,
        quantityBadgeText: 'Price Incl. Qty : 1'
      },
      {
        name: 'Ultimate Adventure',
        price: 0.00,
        quantity: 10,
        currency: '$',
        showQuantityBadge: true,
        quantityBadgeText: 'Price Incl. Qty : 1'
      },
      {
        name: 'Ultimate Adventure',
        price: 0.00,
        quantity: 10,
        currency: '$',
        showQuantityBadge: true,
        quantityBadgeText: 'Price Incl. Qty : 1'
      }
  ]
}; 