export interface PriceDetail {
  label: string;
  value: number;
  currency?: string;
}

export interface PriceDetailsResponse {
  subtotal: PriceDetail;
  tax: PriceDetail;
  total: PriceDetail;
  paymentDone: PriceDetail;
  amountDue: PriceDetail;
}

export const dummyPriceDetailsResponse: PriceDetailsResponse = {
  subtotal: {
    label: 'Subtotal',
    value: 499.50,
    currency: '$'
  },
  tax: {
    label: 'Tax',
    value: 40.00,
    currency: '$'
  },
  total: {
    label: 'Total',
    value: 539.00,
    currency: '$'
  },
  paymentDone: {
    label: 'Payment Done',
    value: 100.00,
    currency: '$'
  },
  amountDue: {
    label: 'Amount Due',
    value: 439.00,
    currency: '$'
  }
};
