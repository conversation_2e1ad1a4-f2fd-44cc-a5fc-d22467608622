import { ChangeDetectionStrategy, Component } from '@angular/core';

@Component({
  selector: 'app-payment-options',
  imports: [],
  templateUrl: './payment-options.component.html',
  styleUrl: './payment-options.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush 
})
export class PaymentOptionsComponent {
  selected: 'deposit' | 'full' = 'deposit';
  select(option: 'deposit' | 'full') {
    this.selected = option;
  }
}
