import { ChangeDetectionStrategy, Component } from '@angular/core';
import { SummaryCardResponse, dummySummaryCardResponse } from '../../models/summary-card.model';

@Component({
  selector: 'app-summary-card',
  imports: [],
  templateUrl: './summary-card.html',
  styleUrl: './summary-card.css',
  changeDetection: ChangeDetectionStrategy.OnPush 
})
export class SummaryCard {
  summaryData: SummaryCardResponse = dummySummaryCardResponse;

  getTitle() {
    return this.summaryData.title;
  }

  getBasePrice() {
    return this.summaryData.basePrice;
  }

  getCurrency() {
    return this.summaryData.currency;
  }

  getLocationInfo() {
    return this.summaryData.locationInfo;
  }

  getPackageItems() {
    return this.summaryData.packageItems;
  }
}
