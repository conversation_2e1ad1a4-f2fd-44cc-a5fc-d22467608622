<div
  class="flex flex-col gap-6 w-[21.5625rem] p-6 h-[43rem] rounded-[1.875rem] bg-white shadow-[0_6px_16px_0_rgba(137,176,189,0.18)]"
>
  <!-- Title and Base Price -->
  <div class="flex flex-col gap-2">
    <span
      class="text-lg leading-[1.5rem] font-semibold text-neutral-900"
      >{{ getTitle() }}</span
    >
    <span
      class="text-sm leading-[1.125rem] font-semibold text-accent-green"
      >Base Price: {{ getCurrency() }}{{ getBasePrice().toFixed(2) }}</span
    >
  </div>
  <div class="flex flex-col gap-3 p-5 rounded-[1.25rem] bg-surface-lightest">
    <div class="flex items-center gap-2">
      <img src="Location.svg" alt="Location" class="w-5 h-5" />
      <span class="text-sm leading-5 font-normal text-neutral-900"
        >{{ getLocationInfo().location }}</span
      >
    </div>
    <div class="flex items-center gap-2">
      <img src="Calendar.svg" alt="Calendar" class="w-5 h-5" />
      <span class="text-sm leading-5 font-normal text-neutral-900"
        >{{ getLocationInfo().date }}</span
      >
    </div>
  </div>
  <h2 class="text-sm leading-[1.125rem] font-semibold">Package Info</h2>

  <div class="flex-1 overflow-y-auto pr-2 scrollbar-hide">
    @for (item of getPackageItems(); track item.name) {
      <div class="flex flex-row items-start justify-between w-full mb-4">
        <div class="flex flex-col">
          <span class="text-sm leading-5 font-normal text-neutral-900">{{ item.name }}</span>
          <span class="text-xs leading-[1.125rem] text-neutral-dark">({{ getCurrency() }}{{ (getBasePrice() / 10).toFixed(2) }} X {{ item.quantity }})</span>
        </div>
        @if (item.showQuantityBadge) {
          <div class="flex flex-col items-end">
            <span class="text-sm leading-4 font-bold text-neutral-700">{{ getCurrency() }}{{ item.price.toFixed(2) }}</span>
            <span class="mt-1 px-2 text-xs leading-[1.3rem] rounded-xl bg-surface-lighter">{{ item.quantityBadgeText }}</span>
          </div>
        } @else {
          <span class="text-sm leading-5 font-bold text-neutral-700">{{ getCurrency() }}{{ item.price.toFixed(2) }}</span>
        }
      </div>
    }
  </div>
</div>

