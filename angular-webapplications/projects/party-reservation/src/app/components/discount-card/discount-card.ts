import { Component,computed,signal,ChangeDetectionStrategy } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DicountAppliedModal } from '../dicount-applied-modal/dicount-applied-modal';

@Component({
  selector: 'app-discount-card',
  imports: [FormsModule, DicountAppliedModal],
  templateUrl: './discount-card.html',
  styleUrl: './discount-card.css',
  changeDetection: ChangeDetectionStrategy.OnPush 
})

export class DiscountCard {
  couponCode = signal('');
  isApplied = signal(false);
  isError = signal(false);
  showModal = signal(false);

  applyCoupon() {
    const code = this.couponCode().trim().toUpperCase();

    if (!this.isApplied()) {
      if (code === 'SUMMERDEAL15') {
        this.isApplied.set(true);
        this.isError.set(false);
        this.showModal.set(true);
      } else {
        this.isApplied.set(false);
        this.isError.set(true);
      }
    } else {
      // Remove coupon
      this.isApplied.set(false);
      this.isError.set(false);
      this.couponCode.set('');
    }
  }

  closeModal() {
    this.showModal.set(false);
  }

  isButtonDisabled = computed(() => !this.couponCode().trim());
}
