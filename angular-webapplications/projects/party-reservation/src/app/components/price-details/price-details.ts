import { ChangeDetectionStrategy, Component } from '@angular/core';
import { dummyPriceDetailsResponse, PriceDetailsResponse } from '../../models/price-details.model';

@Component({
  selector: 'app-price-details',
  imports: [],
  templateUrl: './price-details.html',
  styleUrl: './price-details.css',
  changeDetection: ChangeDetectionStrategy.OnPush 
})
export class PriceDetails {
  priceDetails: PriceDetailsResponse = dummyPriceDetailsResponse;

  getPriceDetailsArray() {
    return [
      this.priceDetails.subtotal,
      this.priceDetails.tax,
      this.priceDetails.total,
      this.priceDetails.paymentDone
    ];
  }

  getAmountDue() {
    return this.priceDetails.amountDue;
  }
}
