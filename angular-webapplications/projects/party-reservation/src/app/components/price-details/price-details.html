<div class="flex flex-col gap-5 md:w-[28.875rem] h-[18.6875rem] p-6 rounded-[1.875rem] bg-white">
  <div class="flex flex-row items-center gap-2">
    <span class="text-sm leading-[1.125rem] font-semibold text-neutral-900">Price Details</span>
  </div>
  <div class="flex flex-col gap-2 w-full">
    @for (item of getPriceDetailsArray(); track item.label) {
      <div class="flex flex-row items-center w-full">
        <div class="flex-1 text-sm leading-5 font-normal text-neutral-900">{{ item.label }}</div>
        <div class="text-sm leading-5 font-normal text-neutral-900">{{ item.currency }}{{ item.value.toFixed(2) }}</div>
      </div>
    }
  </div>
  <div class="my-2 border-t border-surface"></div>
  <div class="flex flex-row items-baseline w-full">
    <div class="flex-1 text-sm leading-5 font-normal text-neutral-900">{{ getAmountDue().label }}</div>
    <div class="text-sm leading-5 font-bold text-neutral-900">{{ getAmountDue().currency }}{{ getAmountDue().value.toFixed(2) }}</div>
  </div>
</div>

