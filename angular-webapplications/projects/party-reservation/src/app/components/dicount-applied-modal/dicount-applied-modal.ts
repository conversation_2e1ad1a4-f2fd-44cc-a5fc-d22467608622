import { Component, input, output } from '@angular/core';

@Component({
  selector: 'app-dicount-applied-modal',
  imports: [],
  templateUrl: './dicount-applied-modal.html',
  styleUrl: './dicount-applied-modal.css'
})
export class DicountAppliedModal {
  readonly code = input<string>();
  readonly discount = input<number>(0);
  readonly closeModal = output<void>();

  onClose() {
    this.closeModal.emit();
  }
}
