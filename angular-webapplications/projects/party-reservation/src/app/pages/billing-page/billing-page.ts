import { Component } from '@angular/core';
import { ReservationCardComponent } from '../../components/reservation-card/reservation-card';
import { SummaryCard } from '../../components/summary-card/summary-card';
import { GuestForm } from '../../components/guest-form/guest-form';
import { PriceDetails } from '../../components/price-details/price-details';
import { RouterOutlet } from '@angular/router';
import { DiscountCard } from '../../components/discount-card/discount-card';
import { PaymentOptionsComponent } from '../../components/payment-options/payment-options.component';
import { FooterComponent } from '../../components/footer/footer';


@Component({
  selector: 'app-billing-page',
  imports: [ 
    RouterOutlet,
    ReservationCardComponent,
    DiscountCard,
    PaymentOptionsComponent,
    PriceDetails,
    GuestForm,
    SummaryCard,
    FooterComponent
],
  templateUrl: './billing-page.html',
  styleUrl: './billing-page.css'
})
export class BillingPage {

}
